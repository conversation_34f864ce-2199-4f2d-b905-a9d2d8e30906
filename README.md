# Email Scraper Flask Application

A Flask web application that scrapes websites for email addresses. The application crawls through all pages on a given website and extracts email addresses found in the content.

## Features

- **Web scraping**: Crawls through all pages on a website
- **Email extraction**: Uses regex patterns to find email addresses
- **Rate limiting**: Configurable delay between requests to be respectful to target websites
- **Duplicate filtering**: Removes duplicate emails and false positives
- **JSON API**: RESTful API with JSON responses
- **Error handling**: Comprehensive error handling for network issues and invalid URLs

## Installation

1. Make sure you have the `email-scape` conda environment activated:
   ```bash
   conda activate email-scape
   ```

2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Start the Flask application (make sure you're in the email-scrape conda environment):
   ```bash
   # Make sure the conda environment is activated
   conda activate email-scrape

   # Run the application
   python app.py
   ```

2. The application will be available at `http://localhost:5001`

### API Endpoints

#### GET /
Returns usage instructions and API documentation.

#### POST /scrape
Scrapes a website for email addresses.

**Request Body:**
```json
{
    "url": "https://example.com",
    "max_pages": 50,  // optional, default: 50, max: 100
    "delay": 1        // optional, default: 1 second
}
```

**Response:**
```json
{
    "url": "https://example.com",
    "emails_found": 5,
    "emails": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ],
    "pages_scraped": 12,
    "parameters": {
        "max_pages": 50,
        "delay": 1
    }
}
```

#### GET /health
Health check endpoint.

### Example Usage with curl

```bash
# Basic scraping
curl -X POST http://localhost:5001/scrape \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'

# With custom parameters
curl -X POST http://localhost:5001/scrape \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com", "max_pages": 20, "delay": 2}'
```

## Configuration

- **max_pages**: Maximum number of pages to scrape (default: 50, maximum: 100)
- **delay**: Delay between requests in seconds (default: 1)

## Notes

- The scraper only follows links within the same domain
- It includes a 1-second delay between requests by default to be respectful to target websites
- The application filters out common false positives (image files, CSS, JS files)
- All found emails are converted to lowercase for consistency

## Error Handling

The application handles various error scenarios:
- Invalid URLs
- Network timeouts
- HTTP errors
- Invalid JSON payloads
- Parameter validation errors
