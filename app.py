from flask import Flask, request, jsonify
import logging
from email_scraper import EmailScraper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/')
def home():
    """Home endpoint with usage instructions."""
    return jsonify({
        "message": "Email Scraper API",
        "usage": "POST to /scrape with JSON body containing 'url' field",
        "example": {
            "url": "https://example.com"
        },
        "optional_parameters": {
            "max_pages": "Maximum number of pages to scrape (default: 50)",
            "delay": "Delay between requests in seconds (default: 1)"
        }
    })

@app.route('/scrape', methods=['POST'])
def scrape_emails():
    """
    Scrape emails from a website.
    
    Expected JSON payload:
    {
        "url": "https://example.com",
        "max_pages": 50,  # optional
        "delay": 1        # optional
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        url = data.get('url')
        if not url:
            return jsonify({"error": "URL is required"}), 400
        
        # Optional parameters
        max_pages = data.get('max_pages', 50)
        delay = data.get('delay', 1)
        
        # Validate parameters
        if not isinstance(max_pages, int) or max_pages <= 0:
            return jsonify({"error": "max_pages must be a positive integer"}), 400
        
        if not isinstance(delay, (int, float)) or delay < 0:
            return jsonify({"error": "delay must be a non-negative number"}), 400
        
        # Limit max_pages to prevent abuse
        max_pages = min(max_pages, 100)
        
        logger.info(f"Starting email scrape for URL: {url}")
        
        # Create scraper instance and scrape
        scraper = EmailScraper(delay=delay, max_pages=max_pages)
        emails = scraper.scrape_website(url)
        
        response = {
            "url": url,
            "emails_found": len(emails),
            "emails": emails,
            "pages_scraped": len(scraper.visited_urls),
            "parameters": {
                "max_pages": max_pages,
                "delay": delay
            }
        }
        
        logger.info(f"Scraping completed. Found {len(emails)} emails from {len(scraper.visited_urls)} pages.")
        return jsonify(response)
    
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        return jsonify({"error": str(e)}), 400
    
    except Exception as e:
        logger.error(f"Unexpected error during scraping: {e}")
        return jsonify({"error": "An unexpected error occurred during scraping"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "service": "email-scraper"})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
